import base64
from urllib.parse import unquote, urlparse, parse_qs

def extract_target_url_from_linkvertise(linkvertise_url):
    """
    Extract the target URL from a linkvertise URL with dynamic format.

    Args:
        linkvertise_url (str): The linkvertise URL with /dynamic and r parameter

    Returns:
        str: The decoded target URL, or None if extraction fails
    """
    try:
        # Parse the URL to get query parameters
        parsed = urlparse(linkvertise_url)
        query_params = parse_qs(parsed.query)

        # Get the 'r' parameter (which contains the base64 encoded target URL)
        if 'r' not in query_params:
            print("No 'r' parameter found in URL")
            return None

        r_param = query_params['r'][0]

        # URL decode first (handles %3D -> = conversion)
        r_param_decoded = unquote(r_param)

        # Base64 decode to get the actual target URL
        target_url = base64.b64decode(r_param_decoded).decode('utf-8')

        return target_url

    except Exception as e:
        print(f"Error extracting target URL: {e}")
        return None

# Your linkvertise URL
linkvertise_url = "https://linkvertise.com/1344279/252.1284066025713/dynamic?r=aHR0cHM6Ly9mcmVlLmZyZWV6ZWhvc3QucHJvL2x2L3JlZGVlbS9pZFBSOTJ5TkZ6bGc%3D&o=sharing"

print("=== Linkvertise URL Bypasser ===")
print(f"Input URL: {linkvertise_url}")
print()

# Extract the target URL
target_url = extract_target_url_from_linkvertise(linkvertise_url)

if target_url:
    print(f"✅ Successfully extracted target URL:")
    print(f"🔗 {target_url}")
    print()
    print("You can now visit this URL directly without going through linkvertise!")
else:
    print("❌ Failed to extract target URL")
    print("The URL format might not be supported by this method.")